name: 🐞 Bug
description: Report unexpected behavior with the library
title: "[BUG] <title>"
labels: [Bug, Needs Triage]
body:
- type: checkboxes
  attributes:
    label: Is there an existing issue for this?
    description: Please search to see if an issue already exists for the bug you encountered.
    options:
    - label: I have searched the existing issues
      required: true
- type: textarea
  attributes:
    label: Current Behavior
    description: A concise description of what you're experiencing.
  validations:
    required: false
- type: textarea
  attributes:
    label: Expected Behavior
    description: A concise description of what you expected to happen.
  validations:
    required: false
- type: textarea
  attributes:
    label: Environment
    description: |
      How to find these values:
        - **GCC**: `gcc --version`
        - **NVCC**: `nvcc --version`
        - **PyTorch**: `python -c "import torch; print(torch.__version__)"`
        - **PyTorch CUDA**: `python -c "import torch; print(torch.version.cuda)"`
        - **TorchSparse**: `python -c "import torchsparse; print(torchsparse.__version__)"`
    value: |
        - GCC:
        - NVCC:
        - PyTorch:
        - PyTorch CUDA:
        - TorchSparse:
    render: markdown
  validations:
    required: false
- type: textarea
  attributes:
    label: Anything else?
    description: |
      Links? References? Anything that will give us more context about the issue you are encountering!

      Tip: You can attach images or log files by clicking this area to highlight it and then dragging files in.
  validations:
    required: false
