repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.0.1
    hooks:
      - id: trailing-whitespace
        name: (Common) Remove trailing whitespaces
      - id: mixed-line-ending
        name: (Common) Fix mixed line ending
        args: [--fix=lf]
      - id: end-of-file-fixer
        name: (Common) Remove extra EOF newlines
      - id: check-merge-conflict
        name: (Common) Check for merge conflicts
      - id: requirements-txt-fixer
        name: (Common) Sort "requirements.txt"
      - id: fix-encoding-pragma
        name: (Python) Remove encoding pragmas
        args: [--remove]
      - id: double-quote-string-fixer
        name: (Python) Fix double-quoted strings
      - id: debug-statements
        name: (Python) Check for debugger imports
      - id: check-json
        name: (JSON) Check syntax
      - id: check-yaml
        name: (YAML) Check syntax
      - id: check-toml
        name: (TOML) Check syntax
  - repo: https://github.com/executablebooks/mdformat
    rev: 0.7.7
    hooks:
      - id: mdformat
        name: (Markdown) Format with mdformat
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.0.0
    hooks:
      - id: pyupgrade
        name: (Python) Update syntax for newer versions
        args: [--py36-plus]
  - repo: https://github.com/google/yapf
    rev: v0.31.0
    hooks:
      - id: yapf
        name: (Python) Format with yapf
  - repo: https://github.com/pycqa/isort
    rev: 5.8.0
    hooks:
      - id: isort
        name: (Python) Sort imports with isort
  - repo: https://github.com/pycqa/flake8
    rev: 3.9.2
    hooks:
      - id: flake8
        name: (Python) Check with flake8
        additional_dependencies:
          - flake8-bugbear
          - flake8-comprehensions
          - flake8-docstrings
          - flake8-executable
          - flake8-quotes
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v0.902
    hooks:
      - id: mypy
        name: (Python) Check with mypy
        additional_dependencies:
          - tokenize-rt
          - types-pyyaml
          - types-toml
  - repo: https://github.com/pre-commit/mirrors-clang-format
    rev: v13.0.0
    hooks:
      - id: clang-format
        name: (C/C++/CUDA) Format with clang-format
        args: [-style=google, -i]
        types_or: [c, c++, cuda]
