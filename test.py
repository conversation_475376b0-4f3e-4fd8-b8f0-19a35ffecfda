from direct3d_s2.pipeline import Direct3DS2Pipeline
import torch # Import torch for CUDA checks

# --- CUDA Check (Optional but Recommended) ---
if torch.cuda.is_available():
    print(f"CUDA is available. Device: {torch.cuda.get_device_name(0)}")
else:
    print("Warning: CUDA is not available. Attempting to use 'cuda:0' will likely fail.")
    # Consider raising an error or falling back to CPU if CUDA is essential
    # raise RuntimeError("CUDA is required but not available.")

# --- Step 1: Load the pipeline model ---
print("Loading pipeline from Hugging Face Hub...")
pipeline_model = Direct3DS2Pipeline.from_pretrained(
  'wushuang98/Direct3D-S2', 
  subfolder="direct3d-s2-v-1-1"
)

if pipeline_model is None:
    raise RuntimeError("Direct3DS2Pipeline.from_pretrained returned None. Model loading failed.")
print("Pipeline model loaded successfully (likely on CPU initially).")

# --- Step 2: Move the pipeline to the CUDA device ---
print("Moving pipeline to cuda:0...")
device_target = "cuda:0"
# Call .to() for its in-place effect.
# Standard PyTorch .to() modifies the module in-place and returns self.
# Even if this specific implementation returns None, we assume it attempts the in-place move.
pipeline_model.to(device_target)

# Assign the pipeline_model (which should now be on the target device) to the pipeline variable.
pipeline = pipeline_model

print(f"Pipeline variable assigned. Assuming model was moved to {device_target} in-place by the .to() call.")

mesh = pipeline(
  'assets/test/13.png', 
  sdf_resolution=512, # 512 or 1024
  remesh=False, # Switch to True if you need to reduce the number of triangles.
)["mesh"]
mesh.export('output.obj')
