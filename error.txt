  x Failed to build `torchsparse @ git+https://github.com/Deathdadev/torchsparse.git@f1787ee10b49a4b5dd22b7d089db8ec2391da52d`
  |-> The build backend returned an error                                                                                                                                                                                                                                                                                      
  `-> Call to `setuptools.build_meta:__legacy__.build_wheel` failed (exit code: 1)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            
      [stdout]
      torchsparse version: 2.1.0
      running bdist_wheel
      running build
      running build_py
      copying torchsparse\backends.py -> build\lib.win-amd64-cpython-310\torchsparse
      copying torchsparse\operators.py -> build\lib.win-amd64-cpython-310\torchsparse
      copying torchsparse\tensor.py -> build\lib.win-amd64-cpython-310\torchsparse
      copying torchsparse\version.py -> build\lib.win-amd64-cpython-310\torchsparse
      copying torchsparse\__init__.py -> build\lib.win-amd64-cpython-310\torchsparse
      copying torchsparse\backbones\resnet.py -> build\lib.win-amd64-cpython-310\torchsparse\backbones
      copying torchsparse\backbones\unet.py -> build\lib.win-amd64-cpython-310\torchsparse\backbones
      copying torchsparse\backbones\__init__.py -> build\lib.win-amd64-cpython-310\torchsparse\backbones
      copying torchsparse\nn\__init__.py -> build\lib.win-amd64-cpython-310\torchsparse\nn
      copying torchsparse\utils\collate.py -> build\lib.win-amd64-cpython-310\torchsparse\utils
      copying torchsparse\utils\quantize.py -> build\lib.win-amd64-cpython-310\torchsparse\utils
      copying torchsparse\utils\tensor_cache.py -> build\lib.win-amd64-cpython-310\torchsparse\utils
      copying torchsparse\utils\to_dense.py -> build\lib.win-amd64-cpython-310\torchsparse\utils
      copying torchsparse\utils\tune.py -> build\lib.win-amd64-cpython-310\torchsparse\utils
      copying torchsparse\utils\utils.py -> build\lib.win-amd64-cpython-310\torchsparse\utils
      copying torchsparse\utils\__init__.py -> build\lib.win-amd64-cpython-310\torchsparse\utils
      copying torchsparse\backbones\modules\blocks.py -> build\lib.win-amd64-cpython-310\torchsparse\backbones\modules
      copying torchsparse\backbones\modules\__init__.py -> build\lib.win-amd64-cpython-310\torchsparse\backbones\modules
      copying torchsparse\nn\functional\activation.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional
      copying torchsparse\nn\functional\count.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional
      copying torchsparse\nn\functional\crop.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional
      copying torchsparse\nn\functional\devoxelize.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional
      copying torchsparse\nn\functional\hash.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional
      copying torchsparse\nn\functional\pooling.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional
      copying torchsparse\nn\functional\query.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional
      copying torchsparse\nn\functional\voxelize.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional
      copying torchsparse\nn\functional\__init__.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional
      copying torchsparse\nn\modules\activation.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\modules
      copying torchsparse\nn\modules\bev.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\modules
      copying torchsparse\nn\modules\conv.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\modules
      copying torchsparse\nn\modules\crop.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\modules
      copying torchsparse\nn\modules\norm.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\modules
      copying torchsparse\nn\modules\pooling.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\modules
      copying torchsparse\nn\modules\__init__.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\modules
      copying torchsparse\nn\utils\apply.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\utils
      copying torchsparse\nn\utils\kernel.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\utils
      copying torchsparse\nn\utils\__init__.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\utils
      copying torchsparse\nn\functional\conv\conv.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv
      copying torchsparse\nn\functional\conv\conv_config.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv
      copying torchsparse\nn\functional\conv\conv_mode.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv
      copying torchsparse\nn\functional\conv\__init__.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv
      copying torchsparse\nn\functional\conv\func\fetch_on_demand.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\func
      copying torchsparse\nn\functional\conv\func\gather_scatter.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\func
      copying torchsparse\nn\functional\conv\func\implicit_gemm.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\func
      copying torchsparse\nn\functional\conv\func\__init__.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\func
      copying torchsparse\nn\functional\conv\hash\hash.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\hash
      copying torchsparse\nn\functional\conv\hash\query.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\hash
      copying torchsparse\nn\functional\conv\hash\__init__.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\hash
      copying torchsparse\nn\functional\conv\kmap\build_kmap.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\kmap
      copying torchsparse\nn\functional\conv\kmap\downsample.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\kmap
      copying torchsparse\nn\functional\conv\kmap\upsample.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\kmap
      copying torchsparse\nn\functional\conv\kmap\__init__.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\kmap
      copying torchsparse\nn\functional\conv\utils\collections.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\utils
      copying torchsparse\nn\functional\conv\utils\compat.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\utils
      copying torchsparse\nn\functional\conv\utils\__init__.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\utils
      copying torchsparse\nn\functional\conv\kmap\func\hashmap.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\kmap\func
      copying torchsparse\nn\functional\conv\kmap\func\hashmap_on_the_fly.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\kmap\func
      copying torchsparse\nn\functional\conv\kmap\func\__init__.py -> build\lib.win-amd64-cpython-310\torchsparse\nn\functional\conv\kmap\func
      running build_ext
      building 'torchsparse.backend' extension
      [1/24] cl /showIncludes /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program
      Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\hashmap\hashmap_cpu.cpp /FoC:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\hashmap\hashmap_cpu.obj /MD /O2 /EHsc
      -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/hashmap/hashmap_cpu.obj
      cl /showIncludes /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program
      Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\hashmap\hashmap_cpu.cpp /FoC:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\hashmap\hashmap_cpu.obj /MD /O2 /EHsc
      -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\hashmap\hashmap_cpu.hpp(7): fatal error C1083: Cannot open include file: 'google/dense_hash_map': No such file or directory
      [2/24] cl /showIncludes /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include      
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program     
      Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files   
      (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\query_cpu.cpp 
      /FoC:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\query_cpu.obj /MD /O2 /EHsc -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/others/query_cpu.obj
      cl /showIncludes /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program     
      Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files   
      (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\query_cpu.cpp 
      /FoC:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\query_cpu.obj /MD /O2 /EHsc -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\query_cpu.cpp(6): fatal error C1083: Cannot open include file: 'google/dense_hash_map': No such file or directory
      [3/24] cl /showIncludes /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include      
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program
      Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\voxelize\voxelize_cpu.cpp /FoC:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\voxelize\voxelize_cpu.obj /MD /O2 /EHsc      
      -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/voxelize/voxelize_cpu.obj
      cl /showIncludes /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program
      Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\voxelize\voxelize_cpu.cpp /FoC:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\voxelize\voxelize_cpu.obj /MD /O2 /EHsc      
      -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\voxelize\voxelize_cpu.cpp(44): fatal error C1060: compiler is out of heap space
      [4/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\downsample_cuda.obj.d -std=c++17 --use-local-env
      -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface
      -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files
      (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\downsample_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\downsample_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__
      --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/others/downsample_cuda.obj
      C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\downsample_cuda.obj.d -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler 
      /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface 
      -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program
      Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\downsample_cuda.cu -o C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\downsample_cuda.obj
      -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89
      -gencode=arch=compute_89,code=sm_89
      Compilation terminated.
      downsample_cuda.cu
      [5/24] cl /showIncludes /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include      
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program     
      Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files
      (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\hash\hash_cpu.cpp    
      /FoC:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\hash\hash_cpu.obj /MD /O2 /EHsc -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/hash/hash_cpu.obj
      cl /showIncludes /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program     
      Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files   
      (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\hash\hash_cpu.cpp    
      /FoC:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\hash\hash_cpu.obj /MD /O2 /EHsc -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\hash\hash_cpu.cpp(60): fatal error C1060: compiler is out of heap space
      [6/24] cl /showIncludes /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include      
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program
      Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\devoxelize\devoxelize_cpu.cpp /FoC:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\devoxelize\devoxelize_cpu.obj /MD /O2    
      /EHsc -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/devoxelize/devoxelize_cpu.obj
      cl /showIncludes /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program
      Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\devoxelize\devoxelize_cpu.cpp /FoC:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\devoxelize\devoxelize_cpu.obj /MD /O2    
      /EHsc -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\devoxelize\devoxelize_cpu.cpp(60): fatal error C1060: compiler is out of heap space
      [7/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_gather_scatter_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows
      Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_gather_scatter_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_gather_scatter_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__
      -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/convolution/convolution_gather_scatter_cuda.obj
      C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_gather_scatter_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows
      Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_gather_scatter_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_gather_scatter_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__
      -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      C:\pinokio\bin\miniconda\include\cpython/pyerrors.h(106): catastrophic error: out of memory
            PyObject *exception,
                      ^

      1 catastrophic error detected in the compilation of "C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/torchsparse/backend/convolution/convolution_gather_scatter_cuda.cu".
      Compilation terminated.
      convolution_gather_scatter_cuda.cu
      [8/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_sorted_cuda.obj.d     
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows
      Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_sorted_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_sorted_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__
      -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/convolution/convolution_backward_wgrad_implicit_gemm_sorted_cuda.obj
      C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_sorted_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows
      Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_sorted_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_sorted_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__
      -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      Compilation terminated.
      convolution_backward_wgrad_implicit_gemm_sorted_cuda.cu
      [9/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_forward_fetch_on_demand_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows
      Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_fetch_on_demand_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_forward_fetch_on_demand_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__
      -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/convolution/convolution_forward_fetch_on_demand_cuda.obj
      C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_forward_fetch_on_demand_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows
      Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_fetch_on_demand_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_forward_fetch_on_demand_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__
      -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      C:/pinokio/api/Direct3D-S2/app/env/lib/site-packages/torch/include/torch/csrc/api/include\torch/nn/modules/loss.h(101): catastrophic error: out of memory
        struct __declspec(dllimport) MSELossImpl : Cloneable<MSELossImpl> {
                                                 ^

      1 catastrophic error detected in the compilation of "C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/torchsparse/backend/convolution/convolution_forward_fetch_on_demand_cuda.cu".
      Compilation terminated.
      convolution_forward_fetch_on_demand_cuda.cu
      [10/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\hash\hash_cuda.obj.d -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler 
      /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface 
      -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program     
      Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files   
      (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\hash\hash_cuda.cu    
      -o C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\hash\hash_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr  
      -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/hash/hash_cuda.obj
      C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\hash\hash_cuda.obj.d -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler
      /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface 
      -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program     
      Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files   
      (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\hash\hash_cuda.cu    
      -o C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\hash\hash_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr  
      -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      Compilation terminated.
      hash_cuda.cu
      [11/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\voxelize\voxelize_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files
      (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\voxelize\voxelize_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\voxelize\voxelize_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__
      --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/voxelize/voxelize_cuda.obj
      C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\voxelize\voxelize_cuda.obj.d -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler 
      /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface 
      -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program
      Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\voxelize\voxelize_cuda.cu -o C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\voxelize\voxelize_cuda.obj
      -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89
      -gencode=arch=compute_89,code=sm_89
      C:/pinokio/api/Direct3D-S2/app/env/lib/site-packages/torch/include\pybind11/pytypes.h(147): catastrophic error: out of memory
            bool is(object_api const &other) const { return derived().ptr() == other.derived().ptr(); }
                    ^

      1 catastrophic error detected in the compilation of "C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/torchsparse/backend/voxelize/voxelize_cuda.cu".
      Compilation terminated.
      voxelize_cuda.cu
      [12/24] cl /showIncludes /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include     
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program     
      Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files   
      (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\count_cpu.cpp 
      /FoC:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\count_cpu.obj /MD /O2 /EHsc -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/others/count_cpu.obj
      cl /showIncludes /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program     
      Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files   
      (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\count_cpu.cpp 
      /FoC:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\count_cpu.obj /MD /O2 /EHsc -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\count_cpu.cpp(24): fatal error C1060: compiler is out of heap space
      [13/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\query_cuda.obj.d -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler
      /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface 
      -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program     
      Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files   
      (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\query_cuda.cu 
      -o C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\query_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__
      --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/others/query_cuda.obj
      C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\query_cuda.obj.d -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler      
      /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface 
      -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program     
      Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files   
      (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\query_cuda.cu 
      -o C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\query_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__
      --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      C:/pinokio/api/Direct3D-S2/app/env/lib/site-packages/torch/include\pybind11/pytypes.h(2221): catastrophic error: out of memory
            public: [[deprecated("Use reinterpret_borrow<" "anyset" ">() or reinterpret_steal<" "anyset" ">()")]] anyset(handle h, bool is_borrowed) : object(is_borrowed ? object(h, borrowed_t{}) : object(h, stolen_t{})) {} anyset(handle h, borrowed_t) : object(h, borrowed_t{}) {} anyset(handle h, stolen_t) :
      object(h, stolen_t{}) {} [[deprecated("Use py::isinstance<py::python_type>(obj) instead")]] bool check() const { return m_ptr != nullptr && ((_Py_IS_TYPE(((const PyObject*)(m_ptr)), &PySet_Type) || _Py_IS_TYPE(((const PyObject*)(m_ptr)), &PyFrozenSet_Type) || PyType_IsSubtype((((PyObject*)(m_ptr))->ob_type),    
      &PySet_Type) || PyType_IsSubtype((((PyObject*)(m_ptr))->ob_type), &PyFrozenSet_Type)) != 0); } static bool check_(handle h) { return h.ptr() != nullptr && (_Py_IS_TYPE(((const PyObject*)(h.ptr())), &PySet_Type) || _Py_IS_TYPE(((const PyObject*)(h.ptr())), &PyFrozenSet_Type) ||
      PyType_IsSubtype((((PyObject*)(h.ptr()))->ob_type), &PySet_Type) || PyType_IsSubtype((((PyObject*)(h.ptr()))->ob_type), &PyFrozenSet_Type)); } template <typename Policy_> anyset(const ::pybind11::detail::accessor<Policy_> &a) : anyset(object(a)) {} anyset(const object &o) : object(o) { if (m_ptr &&
      !check_(m_ptr)) throw ::pybind11::type_error("Object of type '" + ::pybind11::detail::get_fully_qualified_tp_name((((PyObject*)(m_ptr))->ob_type)) + "' is not an instance of '" "anyset" "'"); } anyset(object &&o) : object(std::move(o)) { if (m_ptr && !check_(m_ptr)) throw ::pybind11::type_error("Object of type  
      '" + ::pybind11::detail::get_fully_qualified_tp_name((((PyObject*)(m_ptr))->ob_type)) + "' is not an instance of '" "anyset" "'"); }

      ^

      1 catastrophic error detected in the compilation of "C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/torchsparse/backend/others/query_cuda.cu".
      Compilation terminated.
      query_cuda.cu
      [14/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\reorder_map_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files
      (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\reorder_map_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\reorder_map_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__
      --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/others/reorder_map_cuda.obj
      C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\reorder_map_cuda.obj.d -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler
      /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface 
      -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program
      Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\reorder_map_cuda.cu -o C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\reorder_map_cuda.obj
      -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89
      -gencode=arch=compute_89,code=sm_89
      C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\pybind11\detail/common.h(754): catastrophic error: out of memory
        using any_of = bool_constant<(Ts::value || ...)>;
                                      ^

      1 catastrophic error detected in the compilation of "C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/torchsparse/backend/others/reorder_map_cuda.cu".
      Compilation terminated.
      reorder_map_cuda.cu
      [15/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\devoxelize\devoxelize_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files
      (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\devoxelize\devoxelize_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\devoxelize\devoxelize_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__
      --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/devoxelize/devoxelize_cuda.obj
      C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\devoxelize\devoxelize_cuda.obj.d -std=c++17 --use-local-env
      -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface
      -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files
      (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\devoxelize\devoxelize_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\devoxelize\devoxelize_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__
      --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      Compilation terminated.
      devoxelize_cuda.cu
      [16/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\pybind_cuda.obj.d -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler    
      /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface 
      -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program     
      Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files   
      (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\pybind_cuda.cu -o    
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\pybind_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3    
      -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/pybind_cuda.obj
      C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\pybind_cuda.obj.d -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819    
      -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface
      -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program     
      Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files   
      (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\pybind_cuda.cu -o    
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\pybind_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3    
      -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      Compilation terminated.
      pybind_cuda.cu
      [17/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\count_cuda.obj.d -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler
      /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface 
      -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program     
      Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files   
      (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\count_cuda.cu 
      -o C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\count_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__
      --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/others/count_cuda.obj
      C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\count_cuda.obj.d -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler      
      /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface 
      -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program     
      Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files   
      (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\count_cuda.cu 
      -o C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\count_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__
      --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\pybind11\detail/common.h(754): catastrophic error: out of memory
        using any_of = bool_constant<(Ts::value || ...)>;
                                      ^

      1 catastrophic error detected in the compilation of "C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/torchsparse/backend/others/count_cuda.cu".
      Compilation terminated.
      count_cuda.cu
      [18/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\exclusive_scan_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files
      (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\exclusive_scan_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\exclusive_scan_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__
      --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/others/exclusive_scan_cuda.obj
      C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\exclusive_scan_cuda.obj.d -std=c++17 --use-local-env
      -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe --diag_suppress=base_class_has_different_dll_interface
      -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files
      (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\exclusive_scan_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\exclusive_scan_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__
      --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      Compilation terminated.
      exclusive_scan_cuda.cu
      [19/24] cl /showIncludes /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows
      Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_gather_scatter_cpu.cpp
      /FoC:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_gather_scatter_cpu.obj /MD /O2 /EHsc -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/convolution/convolution_gather_scatter_cpu.obj
      cl /showIncludes /nologo /O2 /W3 /GL /DNDEBUG /MD /MD /wd4819 /wd4251 /wd4244 /wd4267 /wd4275 /wd4018 /wd4190 /wd4624 /wd4067 /wd4068 /EHsc -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows
      Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_gather_scatter_cpu.cpp
      /FoC:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_gather_scatter_cpu.obj /MD /O2 /EHsc -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 /std:c++17
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_gather_scatter_cpu.cpp(184): fatal error C1060: compiler is out of heap space
      [20/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_forward_implicit_gemm_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows
      Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_forward_implicit_gemm_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__
      -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      FAILED: C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/build/temp.win-amd64-cpython-310/Release/torchsparse/backend/convolution/convolution_forward_implicit_gemm_cuda.obj
      C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_forward_implicit_gemm_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows
      Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_forward_implicit_gemm_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__
      -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      C:/pinokio/api/Direct3D-S2/app/env/lib/site-packages/torch/include\pybind11/pybind11.h(1475): catastrophic error: out of memory
        template <typename T, enable_if_t<has_operator_delete<T>::value, int> = 0>
                                                               ^

      1 catastrophic error detected in the compilation of "C:/Users/<USER>/AppData/Local/uv/cache/git-v0/checkouts/756be6f609e645b3/f1787ee/torchsparse/backend/convolution/convolution_forward_implicit_gemm_cuda.cu".
      Compilation terminated.
      convolution_forward_implicit_gemm_cuda.cu
      [21/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\reduce_bitmask_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files
      (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\reduce_bitmask_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\reduce_bitmask_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__
      --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      reduce_bitmask_cuda.cu
      tmpxft_000039f4_00000000-7_reduce_bitmask_cuda.cudafe1.cpp
      [22/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\sparsemapping_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files
      (x86)\Windows Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\others\sparsemapping_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\others\sparsemapping_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__
      --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      sparsemapping_cuda.cu
      tmpxft_000053bc_00000000-7_sparsemapping_cuda.cudafe1.cpp
      [23/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows
      Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__
      -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(286): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_original * kernel_volume;
              ^

      Remark: The warnings can be suppressed with "-diag-suppress <warning-number>"

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(335): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(502): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_original * kernel_volume;
              ^

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(556): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 64
              ^

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(965): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_original * kernel_volume;
              ^

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1014): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1142): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_original * kernel_volume;
              ^

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1196): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 64
              ^

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1529): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1646): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 1834       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_start" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 1834       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_amount" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 1834       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_bound" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 1834       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(76): warning #177-D: variable "B_ld_start" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 1834       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(76): warning #177-D: variable "B_ld_amount" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 1834       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(76): warning #177-D: variable "B_ld_bound" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 1834       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(76): warning #177-D: variable "B_ld_amount_N" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
                                                                 ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 1834       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(76): warning #177-D: variable "B_ld_K_bound" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
                                                                                ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 1834       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(77): warning #177-D: variable "B_ld_K" was declared but never referenced
          bool B_ld_K;
               ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 1834       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 1834       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 1839        

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_start" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 1839        

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_amount" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 1839        

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_bound" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 1839        

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 1839        

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 1844

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_start" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 1844

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_amount" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 1844

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_bound" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 1844

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 1844

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 1849

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_start" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 1849

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_amount" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 1849

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_bound" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 1849

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 1849

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=2, K_ld_check=false, N_ld_check=true]" at line 1854

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_start" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=2, K_ld_check=false, N_ld_check=true]" at line 1854

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_amount" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=2, K_ld_check=false, N_ld_check=true]" at line 1854

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(75): warning #177-D: variable "A_ld_bound" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=2, K_ld_check=false, N_ld_check=true]" at line 1854

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=2, K_ld_check=false, N_ld_check=true]" at line 1854

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1862        

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1862        

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1867

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1867

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1872

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1872

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1877

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1877

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=2, K_ld_check=true, N_ld_check=true]" at line 1882

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=16, N_ld_factor=2, K_ld_check=true, N_ld_check=true]" at line 1882

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1890

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1890

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1895

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1895

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=8, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1900

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=8, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1900

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=8, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1905

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=8, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1905

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=8, N_ld_factor=2, K_ld_check=true, N_ld_check=true]" at line 1910

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=8, N_ld_factor=2, K_ld_check=true, N_ld_check=true]" at line 1910

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1918

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1918

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1923

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1923

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=4, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1928

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=4, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1928

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=4, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1933

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=4, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1933

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=4, N_ld_factor=2, K_ld_check=true, N_ld_check=true]" at line 1938

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=4, N_ld_factor=2, K_ld_check=true, N_ld_check=true]" at line 1938

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=2, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1946

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=2, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1946

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=2, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1951

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=2, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1951

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=2, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1956

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=2, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1956

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=2, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1961

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=2, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1961

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(28): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=2, N_ld_factor=2, K_ld_check=true, N_ld_check=true]" at line 1966

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(88): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, half *, half *, int *, int *, int *, half *) [with K_ld_factor=2, N_ld_factor=2, K_ld_check=true, N_ld_check=true]" at line 1966

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2013  

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(793): warning #177-D: variable "A_ld_start" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2013  

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(793): warning #177-D: variable "A_ld_amount" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2013  

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(793): warning #177-D: variable "A_ld_bound" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2013  

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(794): warning #177-D: variable "B_ld_start" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2013  

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(794): warning #177-D: variable "B_ld_amount" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2013  

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(794): warning #177-D: variable "B_ld_bound" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2013  

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(794): warning #177-D: variable "B_ld_amount_N" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
                                                                 ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2013  

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(794): warning #177-D: variable "B_ld_K_bound" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
                                                                                ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2013  

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(795): warning #177-D: variable "B_ld_K" was declared but never referenced
          bool B_ld_K;
               ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2013  

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2013  

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 2018   

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(793): warning #177-D: variable "A_ld_start" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 2018   

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(793): warning #177-D: variable "A_ld_amount" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 2018   

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(793): warning #177-D: variable "A_ld_bound" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 2018   

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 2018   

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 2023    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(793): warning #177-D: variable "A_ld_start" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 2023    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(793): warning #177-D: variable "A_ld_amount" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 2023    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(793): warning #177-D: variable "A_ld_bound" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 2023    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 2023    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 2028    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(793): warning #177-D: variable "A_ld_start" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 2028    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(793): warning #177-D: variable "A_ld_amount" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 2028    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(793): warning #177-D: variable "A_ld_bound" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 2028    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 2028    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 2036   

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 2036   

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 2041    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 2041    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 2046     

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 2046     

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 2051     

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 2051     

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 2059    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 2059    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 2064     

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 2064     

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=8, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 2069      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=8, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 2069      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=8, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 2074      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=8, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 2074      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 2082    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 2082    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 2087     

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 2087     

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=4, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 2092      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=4, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 2092      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(746): warning #177-D: variable "K_implicit" was declared but never referenced
          int K_implicit = K_tile_padded * kernel_volume;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=4, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 2097      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(806): warning #177-D: variable "B_kernel_offset" was declared but never referenced
          int B_kernel_offset =  threadIdx.y * 256 / 16
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=4, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 2097      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2141    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1398): warning #177-D: variable "A_ld_start" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2141    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1398): warning #177-D: variable "A_ld_amount" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2141    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1398): warning #177-D: variable "A_ld_bound" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2141    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1399): warning #177-D: variable "B_ld_start" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2141    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1399): warning #177-D: variable "B_ld_amount" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2141    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1399): warning #177-D: variable "B_ld_bound" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2141    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1399): warning #177-D: variable "B_ld_amount_N" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
                                                                 ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2141    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1399): warning #177-D: variable "B_ld_K_bound" was declared but never referenced
          int B_ld_start, B_ld_amount, B_ld_bound, B_pred_guard, B_ld_amount_N, B_ld_K_bound;
                                                                                ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2141    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1400): warning #177-D: variable "B_ld_K" was declared but never referenced
          bool B_ld_K;
               ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 2141    

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 2146     

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1398): warning #177-D: variable "A_ld_start" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 2146     

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1398): warning #177-D: variable "A_ld_amount" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 2146     

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1398): warning #177-D: variable "A_ld_bound" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 2146     

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 2151      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1398): warning #177-D: variable "A_ld_start" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 2151      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1398): warning #177-D: variable "A_ld_amount" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 2151      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1398): warning #177-D: variable "A_ld_bound" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 2151      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 2156      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1398): warning #177-D: variable "A_ld_start" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 2156      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1398): warning #177-D: variable "A_ld_amount" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                          ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 2156      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1398): warning #177-D: variable "A_ld_bound" was declared but never referenced
          int A_ld_start, A_ld_amount, A_ld_bound, A_pred_guard;
                                       ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 2156      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 2164     

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 2169      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 2174       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 2179       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 2187      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 2192       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=8, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 2197        

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=8, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 2202        

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 2210      

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 2215       

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=4, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 2220        

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_forward_implicit_gemm_sorted_cuda.cu(1345): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_forward_cuda_setting1_mode1_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, int, int, float *, float *, int *, int *, int *, float *) [with K_ld_factor=4, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 2225        

      convolution_forward_implicit_gemm_sorted_cuda.cu
      tmpxft_0000bdb4_00000000-7_convolution_forward_implicit_gemm_sorted_cuda.cudafe1.cpp
      [24/24] C:\pinokio\bin\miniconda\bin\nvcc --generate-dependencies-with-compile --dependency-output C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.obj.d
      -std=c++17 --use-local-env -Xcompiler /MD -Xcompiler /wd4819 -Xcompiler /wd4251 -Xcompiler /wd4244 -Xcompiler /wd4267 -Xcompiler /wd4275 -Xcompiler /wd4018 -Xcompiler /wd4190 -Xcompiler /wd4624 -Xcompiler /wd4067 -Xcompiler /wd4068 -Xcompiler /EHsc -Xcudafe
      --diag_suppress=base_class_has_different_dll_interface -Xcudafe --diag_suppress=field_without_dll_interface -Xcudafe --diag_suppress=dll_interface_conflict_none_assumed -Xcudafe --diag_suppress=dll_interface_conflict_dllexport_assumed -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include
      -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\torch\csrc\api\include -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\TH -IC:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\include\THC -IC:\pinokio\bin\miniconda\include
      -IC:\pinokio\api\Direct3D-S2\app\env\include -IC:\pinokio\bin\miniconda\include -IC:\pinokio\bin\miniconda\Include "-IC:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Tools\MSVC\14.29.30133\include" "-IC:\Program Files (x86)\Windows
      Kits\NETFXSDK\4.8\include\um" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\ucrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\shared" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\um" "-IC:\Program Files (x86)\Windows
      Kits\10\include\10.0.22621.0\winrt" "-IC:\Program Files (x86)\Windows Kits\10\include\10.0.22621.0\cppwinrt" -c C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu -o
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.obj -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__
      -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -O3 -std=c++17 -DTORCH_API_INCLUDE_EXTENSION_H -DTORCH_EXTENSION_NAME=backend -D_GLIBCXX_USE_CXX11_ABI=0 -gencode=arch=compute_89,code=compute_89 -gencode=arch=compute_89,code=sm_89
      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(405): warning #177-D: variable "kernel_offset" was declared but never referenced
          int kernel_offset = (blockIdx_y / j_factors1) / (K_original / 32);
              ^

      Remark: The warnings can be suppressed with "-diag-suppress <warning-number>"

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1006): warning #177-D: variable "kernel_offset" was declared but never referenced
          int kernel_offset = (blockIdx_y / j_factors1) / (K_original / 32);
              ^

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1451): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 1669

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 1674

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 1679

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 1684

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=16, N_ld_factor=2, K_ld_check=false, N_ld_check=true]" at line 1689

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1697

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1702

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1707

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1712

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=16, N_ld_factor=2, K_ld_check=true, N_ld_check=true]" at line 1717

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1725

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1730

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=8, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1735

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=8, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1740

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=8, N_ld_factor=2, K_ld_check=true, N_ld_check=true]" at line 1745

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1753

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1758

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=4, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1763

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=4, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1768

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=4, N_ld_factor=2, K_ld_check=true, N_ld_check=true]" at line 1773

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=2, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1781

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=2, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1786

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=2, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1791

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=2, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1796

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(26): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f16f16f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, half *, half *, int *, half *) [with K_ld_factor=2, N_ld_factor=2, K_ld_check=true, N_ld_check=true]" at line 1801

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 1836

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 1841

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 1846

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 1851

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1859

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1864

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1869

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1874

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1882

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1887

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=8, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1892

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=8, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1897

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1905

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1910

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=4, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1915

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(709): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_tf32tf32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=4, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1920

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=false]" at line 1956

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=false, N_ld_check=true]" at line 1961

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=false, N_ld_check=true]" at line 1966

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=false, N_ld_check=true]" at line 1971

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 1979

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 1984

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 1989

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=16, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 1994

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 2002

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=8, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 2007

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=8, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 2012

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=8, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 2017

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=false]" at line 2025

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=4, N_ld_factor=16, K_ld_check=true, N_ld_check=true]" at line 2030

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=4, N_ld_factor=8, K_ld_check=true, N_ld_check=true]" at line 2035

      C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\torchsparse\backend\convolution\convolution_backward_wgrad_implicit_gemm_cuda.cu(1229): warning #177-D: variable "blockIdx_x" was declared but never referenced
          int blockIdx_x = 0;
              ^
                detected during instantiation of "void conv_backward_cuda_setting1_mode0_f32f32f32<K_ld_factor,N_ld_factor,K_ld_check,N_ld_check>(int, int, int, int, int, float *, float *, int *, float *) [with K_ld_factor=4, N_ld_factor=4, K_ld_check=true, N_ld_check=true]" at line 2040

      convolution_backward_wgrad_implicit_gemm_cuda.cu
      tmpxft_00009610_00000000-7_convolution_backward_wgrad_implicit_gemm_cuda.cudafe1.cpp
      ninja: build stopped: subcommand failed.

      [stderr]
      C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\utils\cpp_extension.py:448: UserWarning: The detected CUDA version (12.1) has a minor version mismatch with the version that was used to compile PyTorch (12.6). Most likely this shouldn't be a problem.
        warnings.warn(CUDA_MISMATCH_WARN.format(cuda_str_version, torch.version.cuda))
      C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\utils\cpp_extension.py:2059: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation.
      If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].
        warnings.warn(
      Emitting ninja build file C:\Users\<USER>\AppData\Local\uv\cache\git-v0\checkouts\756be6f609e645b3\f1787ee\build\temp.win-amd64-cpython-310\Release\build.ninja...
      Compiling objects...
      Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)
      Traceback (most recent call last):
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\utils\cpp_extension.py", line 2209, in _run_ninja_build
          subprocess.run(
        File "C:\pinokio\bin\miniconda\lib\subprocess.py", line 526, in run
          raise CalledProcessError(retcode, process.args,
      subprocess.CalledProcessError: Command '['ninja', '-v']' returned non-zero exit status 1.

      The above exception was the direct cause of the following exception:

      Traceback (most recent call last):
        File "<string>", line 11, in <module>
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\build_meta.py", line 432, in build_wheel
          return _build(['bdist_wheel'])
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\build_meta.py", line 423, in _build
          return self._build_with_temp_dir(
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\build_meta.py", line 404, in _build_with_temp_dir
          self.run_setup()
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\build_meta.py", line 512, in run_setup
          super().run_setup(setup_script=setup_script)
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\build_meta.py", line 317, in run_setup
          exec(code, locals())
        File "<string>", line 51, in <module>
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\__init__.py", line 115, in setup
          return distutils.core.setup(**attrs)
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\_distutils\core.py", line 186, in setup
          return run_commands(dist)
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\_distutils\core.py", line 202, in run_commands
          dist.run_commands()
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\_distutils\dist.py", line 1002, in run_commands
          self.run_command(cmd)
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\dist.py", line 1102, in run_command
          super().run_command(command)
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\_distutils\dist.py", line 1021, in run_command
          cmd_obj.run()
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\command\bdist_wheel.py", line 370, in run
          self.run_command("build")
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\_distutils\cmd.py", line 357, in run_command
          self.distribution.run_command(command)
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\dist.py", line 1102, in run_command
          super().run_command(command)
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\_distutils\dist.py", line 1021, in run_command
          cmd_obj.run()
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\_distutils\command\build.py", line 135, in run
          self.run_command(cmd_name)
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\_distutils\cmd.py", line 357, in run_command
          self.distribution.run_command(command)
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\dist.py", line 1102, in run_command
          super().run_command(command)
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\_distutils\dist.py", line 1021, in run_command
          cmd_obj.run()
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\command\build_ext.py", line 96, in run
          _build_ext.run(self)
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\_distutils\command\build_ext.py", line 368, in run
          self.build_extensions()
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\utils\cpp_extension.py", line 900, in build_extensions
          build_ext.build_extensions(self)
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\_distutils\command\build_ext.py", line 484, in build_extensions
          self._build_extensions_serial()
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\_distutils\command\build_ext.py", line 510, in _build_extensions_serial
          self.build_extension(ext)
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\command\build_ext.py", line 261, in build_extension
          _build_ext.build_extension(self, ext)
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\setuptools\_distutils\command\build_ext.py", line 565, in build_extension
          objects = self.compiler.compile(
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\utils\cpp_extension.py", line 872, in win_wrap_ninja_compile
          _write_ninja_file_and_compile_objects(
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\utils\cpp_extension.py", line 1869, in _write_ninja_file_and_compile_objects
          _run_ninja_build(
        File "C:\pinokio\api\Direct3D-S2\app\env\lib\site-packages\torch\utils\cpp_extension.py", line 2225, in _run_ninja_build
          raise RuntimeError(message) from e
      RuntimeError: Error compiling objects for extension

      hint: This usually indicates a problem with the package or the build environment.